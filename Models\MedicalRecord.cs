using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class MedicalRecord
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int record_id { get; set; }
        public int patient_id { get; set; }
        public int doctor_id { get; set; }
        public int appointment_id { get; set; }
        public string diagnosis { get; set; }
        public string prescription { get; set; }
        public string notes { get; set; }
        public DateTime record_date { get; set; }

        [ForeignKey("patient_id")]
        public virtual Patient Patient { get; set; }
        
        [Foreign<PERSON>ey("doctor_id")]
        public virtual Doctor Doctor { get; set; }
        
        [Foreign<PERSON><PERSON>("appointment_id")]
        public virtual Appointment Appointment { get; set; }
    }
}
