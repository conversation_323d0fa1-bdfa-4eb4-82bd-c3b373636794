﻿@model Midterm1212.Models.Patient

@{
    ViewData["Title"] = "Patient Details";
}

<div class="row">
    <div class="col-12">
        <h4>Patient Details</h4>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.name)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.dob)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.dob)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.gender)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.gender)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.phone)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.phone)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.address)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.address)
        </dd>
        </dl>

        <div class="mt-4">
            <a asp-action="Edit" asp-route-id="@Model?.patient_id" class="btn btn-warning">Edit</a>
            <a asp-action="Delete" asp-route-id="@Model?.patient_id" class="btn btn-danger">Delete</a>
            <a asp-action="Index" class="btn btn-secondary">Back to List</a>
        </div>
    </div>
</div>
