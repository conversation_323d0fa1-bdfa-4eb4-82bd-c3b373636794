﻿@model IEnumerable<Midterm1212.Models.Patient>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.name)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.dob)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.gender)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.phone)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.address)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.dob)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.gender)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.phone)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.address)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.patient_id">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.patient_id">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.patient_id">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
