﻿@model Midterm1212.Models.Appointment

@{
    ViewData["Title"] = "Create";
}

<h1>Create</h1>

<h4>Appointment</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="appointment_id" class="control-label"></label>
                <input asp-for="appointment_id" class="form-control" />
                <span asp-validation-for="appointment_id" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="patient_id" class="control-label"></label>
                <select asp-for="patient_id" class ="form-control" asp-items="ViewBag.patient_id"></select>
            </div>
            <div class="form-group">
                <label asp-for="doctor_id" class="control-label"></label>
                <select asp-for="doctor_id" class ="form-control" asp-items="ViewBag.doctor_id"></select>
            </div>
            <div class="form-group">
                <label asp-for="appointment_date" class="control-label"></label>
                <input asp-for="appointment_date" class="form-control" />
                <span asp-validation-for="appointment_date" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="appointment_time" class="control-label"></label>
                <input asp-for="appointment_time" class="form-control" />
                <span asp-validation-for="appointment_time" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="status" class="control-label"></label>
                <input asp-for="status" class="form-control" />
                <span asp-validation-for="status" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
