using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class Doctor
    {
        public Doctor()
        {
            Appointments = new HashSet<Appointment>();
            MedicalRecords = new HashSet<MedicalRecord>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int doctor_id { get; set; }

        [Required]
        public string name { get; set; } = string.Empty;

        [Required]
        public string specialization { get; set; } = string.Empty;

        [Required]
        public string phone { get; set; } = string.Empty;

        [Required]
        public string email { get; set; } = string.Empty;

        public string? image { get; set; }

        [Required]
        public string work_experience { get; set; } = string.Empty;

        public virtual ICollection<Appointment> Appointments { get; set; }
        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; }
    }
}
