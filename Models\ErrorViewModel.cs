using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class Patient
    {
        [Key]
        public int patient_id { get; set; }
        public string name { get; set; }
        public DateTime dob { get; set; }
        public string gender { get; set; }
        public string phone { get; set; }
        public string address { get; set; }

        public virtual ICollection<Appointment> Appointments { get; set; }
        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; }
    }

    public class Doctor
    {
        [Key]
        public int doctor_id { get; set; }
        public string name { get; set; }
        public string specialization { get; set; }
        public string phone { get; set; }
        public string email { get; set; }
        public string image { get; set; }
        public string work_experience { get; set; }

        public virtual ICollection<Appointment> Appointments { get; set; }
        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; }
    }

    public class Appointment
    {
        [Key]
        public int appointment_id { get; set; }
        public int patient_id { get; set; }
        public int doctor_id { get; set; }
        public DateTime appointment_date { get; set; }
        public TimeSpan appointment_time { get; set; }
        public string status { get; set; }

        [ForeignKey("patient_id")]
        public virtual Patient Patient { get; set; }

        [ForeignKey("doctor_id")]
        public virtual Doctor Doctor { get; set; }

        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; }
    }

    public class MedicalRecord
    {
        [Key]
        public int record_id { get; set; }
        public int patient_id { get; set; }
        public int doctor_id { get; set; }
        public int appointment_id { get; set; }
        public string diagnosis { get; set; }
        public string prescription { get; set; }
        public string notes { get; set; }
        public DateTime record_date { get; set; }

        [ForeignKey("patient_id")]
        public virtual Patient Patient { get; set; }

        [ForeignKey("doctor_id")]
        public virtual Doctor Doctor { get; set; }

        [ForeignKey("appointment_id")]
        public virtual Appointment Appointment { get; set; }
    }

    public class ErrorViewModel
    {
        public string? RequestId { get; set; }
        public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);
    }
}
