@model Midterm1212.Models.MedicalRecord
@{
    ViewData["Title"] = "Medical Record";
}
<div class="container" style="max-width:900px; margin-top:30px;">
    <h2 style="text-align:center; color:#4A6FA5;">Medical Record</h2>
    <hr />
    <h4 style="color:#4A6FA5;">Patient Information</h4>
    <div style="margin-bottom:20px;">
        <b>Name:</b> @Model.Patient.name<br />
        <b>Doctor:</b> @Model.Doctor.name<br />
        <b>Appointment Date:</b> @Model.Appointment.appointment_date.ToString("yyyy-MM-dd")
    </div>
    <h4 style="color:green;">Medical Details</h4>
    <div style="margin-bottom:20px;">
        <b>Diagnosis:</b> @Model.diagnosis<br />
        <b>Prescription:</b> @Model.prescription<br />
        <b>Notes:</b> @Model.notes
    </div>
    <a asp-action="Index" class="btn btn-link">Back to Appointments</a>
</div> 