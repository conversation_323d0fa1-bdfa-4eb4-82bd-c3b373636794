using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class Appointment
    {
        public Appointment()
        {
            MedicalRecords = new HashSet<MedicalRecord>();
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int appointment_id { get; set; }

        [Required]
        public int patient_id { get; set; }

        [Required]
        public int doctor_id { get; set; }

        [Required]
        public DateTime appointment_date { get; set; }

        [Required]
        public TimeSpan appointment_time { get; set; }

        [Required]
        public string status { get; set; } = string.Empty;

        [ForeignKey("patient_id")]
        public virtual Patient Patient { get; set; }

        [ForeignKey("doctor_id")]
        public virtual Doctor Doctor { get; set; }

        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; }
    }
}
