﻿@model Midterm1212.Models.Patient

@{
    ViewData["Title"] = "Delete Patient";
}

<div class="row">
    <div class="col-12">
        <h4>Delete Patient</h4>
        <p class="text-danger">Are you sure you want to delete this patient?</p>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.name)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.dob)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.dob)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.gender)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.gender)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.phone)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.phone)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.address)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.address)
        </dd>
    </dl>

        <form asp-action="Delete" class="mt-4">
            <input type="hidden" asp-for="patient_id" />
            <input type="submit" value="Delete" class="btn btn-danger" />
            <a asp-action="Index" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
</div>
