@model Midterm1212.Models.Doctor

@{
    ViewData["Title"] = "Doctor Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="bg-dark text-white p-2 mb-3">
                <nav class="navbar navbar-expand-lg navbar-dark">
                    <a class="navbar-brand" href="#">Doctors</a>
                    <div class="navbar-nav ml-auto">
                        <a class="nav-link" href="@Url.Action("Index", "Doctor")">Doctors</a>
                        <a class="nav-link" href="#">Appointments</a>
                    </div>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <h4>Doctor Details</h4>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="text-center">
                @if (!string.IsNullOrEmpty(Model.image))
                {
                    <img src="@Model.image" alt="Doctor Image" class="img-fluid mb-3" style="max-height: 300px; width: auto;">
                }
                else
                {
                    <div class="bg-light p-4 mb-3" style="height: 300px; display: flex; align-items: center; justify-content: center;">
                        <span class="text-muted">No Image Available</span>
                    </div>
                }
            </div>
        </div>
        <div class="col-md-8">
            <table class="table table-borderless">
                <tr>
                    <td><strong>Name:</strong></td>
                    <td>@Html.DisplayFor(model => model.name)</td>
                </tr>
                <tr>
                    <td><strong>Specialization:</strong></td>
                    <td>@Html.DisplayFor(model => model.specialization)</td>
                </tr>
                <tr>
                    <td><strong>Phone:</strong></td>
                    <td>@Html.DisplayFor(model => model.phone)</td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td>@Html.DisplayFor(model => model.email)</td>
                </tr>
                <tr>
                    <td><strong>Experience:</strong></td>
                    <td>@Html.DisplayFor(model => model.work_experience)</td>
                </tr>
            </table>

            <div class="mt-4">
                <a asp-action="Edit" asp-route-id="@Model.doctor_id" class="btn btn-warning">Edit</a>
                <a asp-action="Delete" asp-route-id="@Model.doctor_id" class="btn btn-danger">Delete</a>
                <a asp-action="Index" class="btn btn-secondary">Back to List</a>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <footer class="text-muted">
                © 2025 - Student ID - Your Name
            </footer>
        </div>
    </div>
</div>
