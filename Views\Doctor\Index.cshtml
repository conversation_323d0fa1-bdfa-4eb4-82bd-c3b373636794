@using Midterm1212.Models
@model PaginatedList<Doctor>

@{
    ViewData["Title"] = "Doctors List";
}

<div class="row">
    <div class="col-12 text-center">
        <h2 class="mb-4" style="color: #666;">Doctors List</h2>
    </div>
</div>

<div class="row">
    @foreach (var item in Model)
    {
        <div class="col-md-4 mb-4">
            <div class="card" style="border: 1px solid #ddd; background-color: #f8f9fa;">
                <div class="card-body">
                    <h5 class="card-title" style="color: #666; font-weight: bold;">@Html.DisplayFor(modelItem => item.name)</h5>

                    <p class="card-text" style="color: #666; font-size: 14px;">
                        <strong>Specialization:</strong> @Html.DisplayFor(modelItem => item.specialization)<br>
                        <strong>Phone:</strong> @Html.DisplayFor(modelItem => item.phone)<br>
                        <strong>Email:</strong> @Html.DisplayFor(modelItem => item.email)
                    </p>

                    <div class="text-center mb-3">
                        @if (!string.IsNullOrEmpty(item.image))
                        {
                            <img src="@item.image" alt="Doctor Image" class="img-fluid" style="max-height: 150px; width: auto; border-radius: 5px;">
                        }
                        else
                        {
                            <div class="bg-light d-flex align-items-center justify-content-center" style="height: 150px; border-radius: 5px;">
                                <span class="text-muted">No Image</span>
                            </div>
                        }
                    </div>

                    <div class="text-center">
                        <a asp-action="Details" asp-route-id="@item.doctor_id" class="btn btn-outline-secondary btn-sm me-2">Read more...</a>
                        <button type="button" class="btn btn-primary btn-sm" onclick="alert('Register functionality coming soon!')">Register</button>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <span class="text-muted">Page @Model.PageIndex / @Model.TotalPages</span>
            <nav aria-label="Page navigation">
                <ul class="pagination pagination-sm mb-0">
                    @if (Model.HasPreviousPage)
                    {
                        <li class="page-item">
                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = Model.PageIndex - 1 })">«</a>
                        </li>
                    }

                    @for (int i = Math.Max(1, Model.PageIndex - 2); i <= Math.Min(Model.TotalPages, Model.PageIndex + 2); i++)
                    {
                        <li class="page-item @(i == Model.PageIndex ? "active" : "")">
                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = i })">@i</a>
                        </li>
                    }

                    @if (Model.HasNextPage)
                    {
                        <li class="page-item">
                            <a class="page-link" href="@Url.Action("Index", new { pageNumber = Model.PageIndex + 1 })">»</a>
                        </li>
                    }
                </ul>
            </nav>
        </div>
    </div>
</div>
