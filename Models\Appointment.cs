using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm1212.Models
{
    public class Appointment
    {
        [Key]
        public int appointment_id { get; set; }
        public int patient_id { get; set; }
        public int doctor_id { get; set; }
        public DateTime appointment_date { get; set; }
        public TimeSpan appointment_time { get; set; }
        public string status { get; set; }

        [ForeignKey("patient_id")]
        public virtual Patient Patient { get; set; }
        
        [ForeignKey("doctor_id")]
        public virtual Doctor Doctor { get; set; }

        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; }
    }
}
