﻿@model Midterm1212.Models.Patient

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Patient</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.name)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.dob)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.dob)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.gender)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.gender)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.phone)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.phone)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.address)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.address)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.patient_id">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
