using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Midterm1212.Models;

namespace Midterm1212.Controllers
{
    public class AppointmentsController : Controller
    {
        private readonly MedicalDbContext _context;

        public AppointmentsController(MedicalDbContext context)
        {
            _context = context;
        }

        // GET: Appointments
        public async Task<IActionResult> Index()
        {
            var medicalDbContext = _context.Appointments.Include(a => a.Doctor).Include(a => a.Patient);
            return View(await medicalDbContext.ToListAsync());
        }

        // GET: Appointments/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            return View(appointment);
        }

        // GET: Appointments/Create
        public IActionResult Create()
        {
            ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "email");
            ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "address");
            return View();
        }

        // POST: Appointments/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("appointment_id,patient_id,doctor_id,appointment_date,appointment_time,status")] Appointment appointment)
        {
            if (ModelState.IsValid)
            {
                _context.Add(appointment);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "email", appointment.doctor_id);
            ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "address", appointment.patient_id);
            return View(appointment);
        }

        // GET: Appointments/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment == null)
            {
                return NotFound();
            }
            ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "email", appointment.doctor_id);
            ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "address", appointment.patient_id);
            return View(appointment);
        }

        // POST: Appointments/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("appointment_id,patient_id,doctor_id,appointment_date,appointment_time,status")] Appointment appointment)
        {
            if (id != appointment.appointment_id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(appointment);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!AppointmentExists(appointment.appointment_id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["doctor_id"] = new SelectList(_context.Doctors, "doctor_id", "email", appointment.doctor_id);
            ViewData["patient_id"] = new SelectList(_context.Patients, "patient_id", "address", appointment.patient_id);
            return View(appointment);
        }

        // GET: Appointments/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            return View(appointment);
        }

        // POST: Appointments/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var appointment = await _context.Appointments.FindAsync(id);
            if (appointment != null)
            {
                _context.Appointments.Remove(appointment);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        // GET: Appointments/Pending/5
        public async Task<IActionResult> Pending(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            ViewBag.Appointment = appointment;
            return View();
        }

        // POST: Appointments/Pending/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Pending(int id, string diagnosis, string prescription, string notes)
        {
            var appointment = await _context.Appointments
                .Include(a => a.Doctor)
                .Include(a => a.Patient)
                .FirstOrDefaultAsync(m => m.appointment_id == id);
            if (appointment == null)
            {
                return NotFound();
            }

            var existingRecord = await _context.MedicalRecords
                .FirstOrDefaultAsync(r => r.appointment_id == id);
            if (existingRecord != null)
            {
                return RedirectToAction("Complete", new { id = id });
            }

            var maxRecordId = await _context.MedicalRecords.AnyAsync()
                ? await _context.MedicalRecords.MaxAsync(r => r.record_id)
                : 0;

            var record = new MedicalRecord
            {
                record_id = maxRecordId + 1,
                patient_id = appointment.patient_id,
                doctor_id = appointment.doctor_id,
                appointment_id = appointment.appointment_id,
                diagnosis = diagnosis,
                prescription = prescription,
                notes = notes,
                record_date = DateTime.Now
            };
            _context.MedicalRecords.Add(record);
            await _context.SaveChangesAsync();

            await _context.Appointments
                .Where(a => a.appointment_id == id)
                .ExecuteUpdateAsync(a => a.SetProperty(p => p.status, "Complete"));
            return RedirectToAction("Complete", new { id = id });
        }

        // GET: Appointments/Complete/5
        public async Task<IActionResult> Complete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var record = await _context.MedicalRecords
                .Include(r => r.Doctor)
                .Include(r => r.Patient)
                .Include(r => r.Appointment)
                .FirstOrDefaultAsync(r => r.appointment_id == id);
            if (record == null)
            {
                return NotFound();
            }

            return View(record);
        }

        private bool AppointmentExists(int id)
        {
            return _context.Appointments.Any(e => e.appointment_id == id);
        }
    }
}
