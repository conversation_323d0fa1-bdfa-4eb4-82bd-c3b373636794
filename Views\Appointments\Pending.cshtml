@{
    ViewData["Title"] = "Pending";
    var appointment = ViewBag.Appointment;
}
<div class="container" style="max-width:600px; margin-top:30px;">
    <div class="panel panel-default">
        <div class="panel-body">
            <p><b>Patient:</b> @appointment.Patient.name</p>
            <p><b>Doctor:</b> @appointment.Doctor.name</p>
            <form asp-action="Pending" asp-route-id="@appointment.appointment_id" method="post">
                <div class="form-group">
                    <label><b>Diagnosis</b></label>
                    <input type="text" name="diagnosis" class="form-control" />
                </div>
                <div class="form-group">
                    <label><b>Prescription</b></label>
                    <input type="text" name="prescription" class="form-control" />
                </div>
                <div class="form-group">
                    <label><b>Notes</b></label>
                    <textarea name="notes" class="form-control" rows="3"></textarea>
                </div>
                <button type="submit" class="btn btn-success" style="margin-top:10px;">Save</button>
            </form>
        </div>
    </div>
</div> 