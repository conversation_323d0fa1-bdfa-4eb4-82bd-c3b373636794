using System.ComponentModel.DataAnnotations;

namespace Midterm1212.Models
{
    public class Patient
    {
        [Key]
        public int patient_id { get; set; }
        public string name { get; set; }
        public DateTime dob { get; set; }
        public string gender { get; set; }
        public string phone { get; set; }
        public string address { get; set; }

        public virtual ICollection<Appointment> Appointments { get; set; }
        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; }
    }
}
