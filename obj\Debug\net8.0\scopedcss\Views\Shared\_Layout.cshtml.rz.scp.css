/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-yiw6syusto] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-yiw6syusto] {
  color: #0077cc;
}

.btn-primary[b-yiw6syusto] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-yiw6syusto], .nav-pills .show > .nav-link[b-yiw6syusto] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-yiw6syusto] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-yiw6syusto] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-yiw6syusto] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-yiw6syusto] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-yiw6syusto] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
