﻿@model Midterm1212.Models.Patient

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Patient</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.name)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.name)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.dob)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.dob)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.gender)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.gender)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.phone)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.phone)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.address)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.address)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="patient_id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
