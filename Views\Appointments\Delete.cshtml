﻿@model Midterm1212.Models.Appointment

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Appointment</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.appointment_date)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.appointment_date)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.appointment_time)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.appointment_time)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.status)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.status)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Patient)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Patient.address)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Doctor)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Doctor.email)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="appointment_id" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
