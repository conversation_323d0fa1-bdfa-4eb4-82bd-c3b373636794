﻿@model Midterm1212.Models.Appointment

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Appointment</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.appointment_date)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.appointment_date)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.appointment_time)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.appointment_time)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.status)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.status)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Patient)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Patient.address)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Doctor)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Doctor.email)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.appointment_id">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
