@model IEnumerable<Midterm1212.Models.Doctor>

@{
    ViewData["Title"] = "Doctors List";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="bg-dark text-white p-2 mb-3">
                <nav class="navbar navbar-expand-lg navbar-dark">
                    <a class="navbar-brand" href="#">Doctors</a>
                    <div class="navbar-nav ml-auto">
                        <a class="nav-link" href="@Url.Action("Index", "Doctor")">Doctors</a>
                        <a class="nav-link" href="#">Appointments</a>
                    </div>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <h4>Doctors List</h4>
            <p>
                <a asp-action="Create" class="btn btn-primary">Create New</a>
            </p>
        </div>
    </div>

    <div class="row">
        @foreach (var item in Model)
        {
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">@Html.DisplayFor(modelItem => item.name)</h6>
                        <p class="card-text">
                            <strong>Specialization:</strong> @Html.DisplayFor(modelItem => item.specialization)<br>
                            <strong>Phone:</strong> @Html.DisplayFor(modelItem => item.phone)<br>
                            <strong>Email:</strong> @Html.DisplayFor(modelItem => item.email)
                        </p>
                        <div class="mb-3">
                            @if (!string.IsNullOrEmpty(item.image))
                            {
                                <img src="@item.image" alt="Doctor Image" class="img-fluid" style="max-height: 150px; width: auto;">
                            }
                            else
                            {
                                <div class="bg-light p-4" style="height: 150px; display: flex; align-items: center; justify-content: center;">
                                    <span class="text-muted">No Image</span>
                                </div>
                            }
                        </div>
                        <div class="btn-group" role="group">
                            <a asp-action="Details" asp-route-id="@item.doctor_id" class="btn btn-info btn-sm">Details</a>
                            <a asp-action="Edit" asp-route-id="@item.doctor_id" class="btn btn-warning btn-sm">Edit</a>
                            <a asp-action="Delete" asp-route-id="@item.doctor_id" class="btn btn-danger btn-sm">Delete</a>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>

    <div class="row">
        <div class="col-12">
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    <li class="page-item active">
                        <a class="page-link" href="#">1</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">2</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <footer class="text-muted">
                © 2025 - Student ID - Your Name
            </footer>
        </div>
    </div>
</div>
