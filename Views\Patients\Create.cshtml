﻿@model Midterm1212.Models.Patient

@{
    ViewData["Title"] = "Create Patient";
}

<div class="row">
    <div class="col-12">
        <h4>Create</h4>
        <h6>Patient</h6>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <form asp-action="Create">
            <div asp-validation-summary="All" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="patient_id" class="control-label"></label>
                <input asp-for="patient_id" class="form-control" />
                <span asp-validation-for="patient_id" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="name" class="control-label"></label>
                <input asp-for="name" class="form-control" />
                <span asp-validation-for="name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="dob" class="control-label"></label>
                <input asp-for="dob" class="form-control" type="date" />
                <span asp-validation-for="dob" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="gender" class="control-label"></label>
                <select asp-for="gender" class="form-control">
                    <option value="">Select Gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                </select>
                <span asp-validation-for="gender" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="phone" class="control-label"></label>
                <input asp-for="phone" class="form-control" />
                <span asp-validation-for="phone" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="address" class="control-label"></label>
                <input asp-for="address" class="form-control" />
                <span asp-validation-for="address" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-success" />
                <a asp-action="Index" class="btn btn-secondary">Back to List</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
