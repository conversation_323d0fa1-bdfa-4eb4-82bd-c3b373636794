@model Midterm1212.Models.Doctor

@{
    ViewData["Title"] = "Delete Doctor";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="bg-dark text-white p-2 mb-3">
                <nav class="navbar navbar-expand-lg navbar-dark">
                    <a class="navbar-brand" href="#">Doctors</a>
                    <div class="navbar-nav ml-auto">
                        <a class="nav-link" href="@Url.Action("Index", "Doctor")">Doctors</a>
                        <a class="nav-link" href="#">Appointments</a>
                    </div>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <h4>Delete Doctor</h4>
            <p class="text-danger">Are you sure you want to delete this doctor?</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="text-center">
                @if (!string.IsNullOrEmpty(Model.image))
                {
                    <img src="@Model.image" alt="Doctor Image" class="img-fluid mb-3" style="max-height: 200px; width: auto;">
                }
                else
                {
                    <div class="bg-light p-4 mb-3" style="height: 200px; display: flex; align-items: center; justify-content: center;">
                        <span class="text-muted">No Image Available</span>
                    </div>
                }
            </div>
        </div>
        <div class="col-md-8">
            <table class="table table-borderless">
                <tr>
                    <td><strong>Name:</strong></td>
                    <td>@Html.DisplayFor(model => model.name)</td>
                </tr>
                <tr>
                    <td><strong>Specialization:</strong></td>
                    <td>@Html.DisplayFor(model => model.specialization)</td>
                </tr>
                <tr>
                    <td><strong>Phone:</strong></td>
                    <td>@Html.DisplayFor(model => model.phone)</td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td>@Html.DisplayFor(model => model.email)</td>
                </tr>
                <tr>
                    <td><strong>Experience:</strong></td>
                    <td>@Html.DisplayFor(model => model.work_experience)</td>
                </tr>
            </table>

            <form asp-action="Delete" class="mt-4">
                <input type="hidden" asp-for="doctor_id" />
                <input type="submit" value="Delete" class="btn btn-danger" />
                <a asp-action="Index" class="btn btn-secondary">Cancel</a>
            </form>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <footer class="text-muted">
                © 2025 - Student ID - Your Name
            </footer>
        </div>
    </div>
</div>
