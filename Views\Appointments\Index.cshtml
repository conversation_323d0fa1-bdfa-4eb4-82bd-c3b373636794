﻿@model IEnumerable<Midterm1212.Models.Appointment>

@{
    ViewData["Title"] = "Appointments";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                <div class="collapse navbar-collapse">
                    <ul class="navbar-nav mr-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="#">Doctors</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">Patients</a>
                        </li>
                        <li class="nav-item active">
                            <a class="nav-link" href="#">Appointments <span class="sr-only">(current)</span></a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-md-12">
            <h1>Appointment Schedule</h1>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Patient</th>
                        <th>Doctor</th>
                        <th>Date</th>
                        <th>Time</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>
                                @Html.DisplayFor(modelItem => item.Patient.name)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.Doctor.name)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.appointment_date)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.appointment_time)
                            </td>
                            <td>
                                @{
                                    string statusClass = "";
                                    if (item.status == "Pending")
                                    {
                                        statusClass = "badge badge-secondary";
                                    }
                                    else if (item.status == "Complete")
                                    {
                                        statusClass = "badge badge-success";
                                    }
                                }
                                <span class="@statusClass">@Html.DisplayFor(modelItem => item.status)</span>
                            </td>
                            <td>
                                @if (item.status == "Pending")
                                {
                                    <a asp-action="Complete" asp-route-id="@item.appointment_id" class="btn btn-warning btn-sm">Pending</a>
                                }
                                else if (item.status == "Complete")
                                {
                                    <a asp-action="Pending" asp-route-id="@item.appointment_id" class="btn btn-success btn-sm">Complete</a>
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    <div class="row mt-4">
        <div class="col-md-12 text-left">
            © 2025 - Student's ID - Your Name
        </div>
    </div>
</div>