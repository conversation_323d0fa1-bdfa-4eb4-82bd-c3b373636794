using System.ComponentModel.DataAnnotations;

namespace Midterm1212.Models
{
    public class Doctor
    {
        [Key]
        public int doctor_id { get; set; }
        public string name { get; set; }
        public string specialization { get; set; }
        public string phone { get; set; }
        public string email { get; set; }
        public string image { get; set; }
        public string work_experience { get; set; }

        public virtual ICollection<Appointment> Appointments { get; set; }
        public virtual ICollection<MedicalRecord> MedicalRecords { get; set; }
    }
}
