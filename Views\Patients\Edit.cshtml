﻿@model Midterm1212.Models.Patient

@{
    ViewData["Title"] = "Edit";
}

<h1>Edit</h1>

<h4>Patient</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="patient_id" />
            <div class="form-group">
                <label asp-for="name" class="control-label"></label>
                <input asp-for="name" class="form-control" />
                <span asp-validation-for="name" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="dob" class="control-label"></label>
                <input asp-for="dob" class="form-control" />
                <span asp-validation-for="dob" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="gender" class="control-label"></label>
                <input asp-for="gender" class="form-control" />
                <span asp-validation-for="gender" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="phone" class="control-label"></label>
                <input asp-for="phone" class="form-control" />
                <span asp-validation-for="phone" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="address" class="control-label"></label>
                <input asp-for="address" class="form-control" />
                <span asp-validation-for="address" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
