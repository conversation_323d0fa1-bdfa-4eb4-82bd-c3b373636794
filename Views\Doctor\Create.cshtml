@model Midterm1212.Models.Doctor

@{
    ViewData["Title"] = "Create Doctor";
}

<div class="row">
    <div class="col-12">
        <h4>Create</h4>
        <h6>Doctor</h6>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
            <form asp-action="Create" enctype="multipart/form-data">
                <div asp-validation-summary="All" class="text-danger"></div>
                
                <div class="form-group row">
                    <label asp-for="doctor_id" class="col-sm-3 col-form-label">ID:</label>
                    <div class="col-sm-9">
                        <input asp-for="doctor_id" class="form-control" />
                        <span asp-validation-for="doctor_id" class="text-danger"></span>
                    </div>
                </div>

                <div class="form-group row">
                    <label asp-for="name" class="col-sm-3 col-form-label">Name:</label>
                    <div class="col-sm-9">
                        <input asp-for="name" class="form-control" />
                        <span asp-validation-for="name" class="text-danger"></span>
                    </div>
                </div>

                <div class="form-group row">
                    <label asp-for="specialization" class="col-sm-3 col-form-label">Specialization:</label>
                    <div class="col-sm-9">
                        <input asp-for="specialization" class="form-control" />
                        <span asp-validation-for="specialization" class="text-danger"></span>
                    </div>
                </div>

                <div class="form-group row">
                    <label asp-for="phone" class="col-sm-3 col-form-label">Phone:</label>
                    <div class="col-sm-9">
                        <input asp-for="phone" class="form-control" />
                        <span asp-validation-for="phone" class="text-danger"></span>
                    </div>
                </div>

                <div class="form-group row">
                    <label asp-for="email" class="col-sm-3 col-form-label">Email:</label>
                    <div class="col-sm-9">
                        <input asp-for="email" class="form-control" />
                        <span asp-validation-for="email" class="text-danger"></span>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-sm-3 col-form-label">Image:</label>
                    <div class="col-sm-9">
                        <input type="file" name="imageFile" id="imageFile" class="form-control" accept="image/*" onchange="previewImage(this)" />
                        <div class="mt-2">
                            <img id="imagePreview" src="#" alt="Image Preview" class="img-thumbnail" style="max-height: 150px; width: auto; display: none;">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label asp-for="work_experience" class="col-sm-3 col-form-label">Work Experience:</label>
                    <div class="col-sm-9">
                        <textarea asp-for="work_experience" class="form-control" rows="3"></textarea>
                        <span asp-validation-for="work_experience" class="text-danger"></span>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-sm-9 offset-sm-3">
                        <input type="submit" value="Create" class="btn btn-success" />
                        <a asp-action="Index" class="btn btn-secondary">Back to List</a>
                    </div>
                </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        function previewImage(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('imagePreview').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                }
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
}
